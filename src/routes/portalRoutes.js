import { Router } from "itty-router";
import { PortalController } from "../controllers/portalController";

export function createPortalRouter(env) {
  const router = Router({ base: "/api/portal" });
  const portalController = new PortalController(env);

  // Portal user registration endpoint
  router.post("/users", (request) => portalController.registerUser(request));

  // Portal user activation endpoint
  router.get("/activate", (request) => portalController.activateUser(request));

  // Portal user login endpoint
  router.post("/login", (request) => portalController.login(request));

  router.post("/forgotpassword", (request) =>
    portalController.forgotPassword(request)
  );

  router.post("/reset-password", (request) =>
    portalController.resetPassword(request)
  );

  // Google OAuth routes
  router.get("/auth/google", (request) =>
    portalController.redirectToGoogle(request)
  );
  router.get("/auth/google/callback", (request) =>
    portalController.handleGoogleCallback(request)
  );

  // Test endpoint for creating Google user
  router.post("/auth/test-create-user", (request) =>
    portalController.testCreateGoogleUser(request)
  );

  // Debug endpoint for KV store
  router.get("/debug/kv", (request) => portalController.debugKVStore(request));

  // Get user data endpoint
  router.get("/user", (request) => portalController.getUserData(request));

  // Portal dashboard endpoint (with token authentication)
  router.get("/dashboard", (request) => portalController.getDashboard(request));

  // Portal dashboard endpoint by email (new endpoint)
  router.get("/dashboard-by-email", (request) => portalController.getDashboardByEmail(request));

  // Delete user endpoint
  router.delete("/users", (request) => portalController.deleteUser(request));

  return router;
}
