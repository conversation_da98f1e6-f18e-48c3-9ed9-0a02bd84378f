// src/routes/subscriptionRoutes.js
import { Router } from "itty-router";
import { SubscriptionController } from "../controllers/subscriptionController.js";
import { TierService } from "../services/tierServiceD1.js";

export function createSubscriptionRouter(env) {
  const router = Router({ base: "/api/subscriptions" });
  const controller = new SubscriptionController(env);
  const tierService = new TierServiceD1(env);

  // Create subscription endpoint
  router.post("/", (request) => controller.createSubscription(request));

  // Handle PayPal subscription success
  router.get("/success", async (request) => {
    try {
      const url = new URL(request.url);
      const baToken = url.searchParams.get("ba_token");
      console.log("PayPal success callback URL:", request.url);
      console.log("BA Token:", baToken);

      if (!baToken) {
        throw new Error("Missing billing agreement token");
      }

      // Get subscription data from KV using the ba_token
      const subscriptionData = await env.USERS_KV.get(
        `ba_token:${baToken}`,
        "json"
      );
      console.log("Found subscription data for token:", subscriptionData);

      if (!subscriptionData) {
        throw new Error("No subscription found for this token");
      }

      // Immediately upgrade the user's tier
      await tierService.updateUserTier(subscriptionData.userId, {
        tier: subscriptionData.tier,
        subscriptionId: subscriptionData.subscriptionId,
        addons: subscriptionData.addons || { addon1: false, addon2: false },
        status: "active",
        startDate: new Date().toISOString(),
      });

      // Clean up the temporary token mapping
      await env.USERS_KV.delete(`ba_token:${baToken}`);

      // Redirect to frontend with success status
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "success");
      redirectUrl.searchParams.set(
        "subscription_id",
        subscriptionData.subscriptionId
      );

      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling subscription success:", error);

      // Redirect to frontend with error status
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "error");
      redirectUrl.searchParams.set("error", error.message);

      return Response.redirect(redirectUrl.toString(), 302);
    }
  });

  // Handle PayPal subscription cancellation
  router.get("/cancel", (request) => {
    const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = new URL("/dashboard", frontendUrl);
    redirectUrl.searchParams.set("subscription_status", "cancelled");
    return Response.redirect(redirectUrl.toString(), 302);
  });

  // Update subscription addons
  router.patch("/:subscriptionId/addons", (request) =>
    controller.updateSubscriptionAddons(request)
  );

  // Get subscription status
  router.get("/:subscriptionId/status", (request) =>
    controller.getSubscriptionStatus(request)
  );

  // Add this to the existing createSubscriptionRouter function
  router.post("/xendit", (request) =>
    controller.createXenditSubscription(request)
  );

  // Add success callback endpoint
  router.get("/xendit/success", (request) =>
    controller.handleXenditSuccess(request)
  );

  // Handle 404s
  router.all(
    "*",
    () =>
      new Response(
        JSON.stringify({
          success: false,
          error: "Not Found",
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      )
  );

  return router;
}
