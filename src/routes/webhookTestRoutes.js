// src/routes/webhookTestRoutes.js
import { Router } from "itty-router";
import { WebhookTestController } from "../controllers/webhookTestController";
import { TierService } from "../services/tierServiceD1.js";

export function createWebhookTestRouter(env) {
  const router = Router({ base: "/api/test/webhooks" });
  const controller = new WebhookTestController(env);
  const tierService = new TierServiceD1(env);

  // Endpoint to simulate PayPal webhooks
  router.post("/simulate/paypal", (request) =>
    controller.simulatePayPalWebhook(request)
  );

  // Endpoint to simulate PayPal subscription activation
  router.post("/simulate/paypal/activate", async (request) => {
    try {
      const { subscriptionId, userId, tier } = await request.json();

      if (!subscriptionId || !userId || !tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "subscriptionId, userId, and tier are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get user email if userId is not an email
      let email = userId;
      if (!userId.includes("@")) {
        const userEmail = await tierService.getEmailFromUserId(userId);
        if (!userEmail) {
          return new Response(
            JSON.stringify({
              success: false,
              message: "User not found",
            }),
            {
              status: 404,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        email = userEmail;
      }

      // Upgrade the tier
      const result = await tierService.upgradeEmailTier(email, tier, {
        subscriptionId,
        addon1: false,
        addon2: false,
      });

      return new Response(
        JSON.stringify({
          success: true,
          message: "Subscription activated successfully",
          data: {
            email,
            subscriptionId,
            tier,
            timestamp: new Date().toISOString(),
            status: result,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error simulating webhook:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  return router;
}
