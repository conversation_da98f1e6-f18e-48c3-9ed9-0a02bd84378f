import { XenditService } from "../services/xenditService.js";
import { TierService } from "../services/tierServiceD1.js";
import { ResponseService } from "../services/responseService.js";

export class XenditPurchaseController {
  constructor(env) {
    this.env = env;
    this.xenditService = new XenditService(env);
    this.tierService = new TierServiceD1(env);
    this.responseService = new ResponseService();
  }

  async createPurchase(request) {
    try {
      const body = await request.json();
      const {
        tier,
        email,
        amount,
        payment_method = "QRIS", // Default to QRIS
        addons = { addon1: false, addon2: false },
      } = body;

      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Email is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      if (!amount || typeof amount !== "number" || amount <= 0) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Valid amount is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Generate unique external_id for this purchase
      const external_id = `purchase_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      let purchaseResult;

      // Create payment based on method
      switch (payment_method.toUpperCase()) {
        case "QRIS":
          purchaseResult = await this.createQRISPurchase(external_id, email, tier, amount, addons);
          break;
        case "VIRTUAL_ACCOUNT":
        case "VA":
          const bank_code = body.bank_code || "BCA";
          purchaseResult = await this.createVAPurchase(external_id, email, tier, amount, addons, bank_code);
          break;
        case "INVOICE":
          purchaseResult = await this.createInvoicePurchase(external_id, email, tier, amount, addons);
          break;
        default:
          return new Response(
            JSON.stringify({
              success: false,
              message: "Unsupported payment method. Use QRIS, VIRTUAL_ACCOUNT, or INVOICE",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
      }

      // Store purchase data in KV for later processing
      const purchaseData = {
        external_id,
        email,
        tier,
        amount,
        addons,
        payment_method: payment_method.toUpperCase(),
        status: "PENDING",
        created_at: new Date().toISOString(),
      };

      await this.env.USERS_KV.put(
        `xendit_purchase:${external_id}`,
        JSON.stringify(purchaseData)
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            external_id,
            payment_method: payment_method.toUpperCase(),
            tier,
            amount,
            status: "PENDING",
            ...purchaseResult,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Xendit purchase creation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to create purchase",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async createQRISPurchase(external_id, email, tier, amount, addons) {
    const qrisPayment = await this.xenditService.createQRISPayment({
      external_id,
      amount,
      description: `Purchase ${tier} plan for ${email}`,
    });

    return {
      payment_id: qrisPayment.id,
      qr_string: qrisPayment.qr_string,
      qr_code_url: `${this.env.APP_URL}/api/xendit/qris/qr-code`,
      expires_at: qrisPayment.expires_at || new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour
    };
  }

  async createVAPurchase(external_id, email, tier, amount, addons, bank_code) {
    const vaPayment = await this.xenditService.createVirtualAccount({
      external_id,
      bank_code,
      name: email.split('@')[0].toUpperCase(), // Use email prefix as name
      amount,
      is_closed: true,
      description: `Purchase ${tier} plan for ${email}`,
      expiration_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    });

    return {
      payment_id: vaPayment.id,
      bank_code: vaPayment.bank_code,
      account_number: vaPayment.account_number,
      merchant_code: vaPayment.merchant_code,
      expires_at: vaPayment.expiration_date,
    };
  }

  async createInvoicePurchase(external_id, email, tier, amount, addons) {
    const invoice = await this.xenditService.createInvoice({
      external_id,
      amount,
      payer_email: email,
      description: `Purchase ${tier} plan`,
      currency: "IDR",
      payment_methods: ["QRIS", "BANK_TRANSFER", "EWALLET"],
      should_send_email: true,
      success_redirect_url: `${this.env.APP_URL}/api/xendit/purchases/success?external_id=${external_id}`,
      failure_redirect_url: `${this.env.APP_URL}/api/xendit/purchases/failed?external_id=${external_id}`,
    });

    return {
      payment_id: invoice.id,
      invoice_url: invoice.invoice_url,
      expires_at: invoice.expiry_date,
    };
  }

  async handlePurchaseSuccess(request) {
    try {
      const url = new URL(request.url);
      const external_id = url.searchParams.get("external_id");

      if (!external_id) {
        throw new Error("Missing external_id");
      }

      // Get purchase data from KV
      const purchaseData = await this.env.USERS_KV.get(
        `xendit_purchase:${external_id}`,
        "json"
      );

      if (!purchaseData) {
        throw new Error("No purchase found for this external_id");
      }

      // Update user's tier with lifetime access
      await this.tierService.upgradeEmailTier(
        purchaseData.email,
        purchaseData.tier,
        {
          purchaseId: external_id,
          addon1: purchaseData.addons?.addon1 || false,
          addon2: purchaseData.addons?.addon2 || false,
          isPermanent: true, // This indicates it's a lifetime purchase
          purchaseDate: new Date().toISOString(),
          paymentMethod: "xendit",
        }
      );

      // Update purchase status
      purchaseData.status = "COMPLETED";
      purchaseData.completed_at = new Date().toISOString();
      await this.env.USERS_KV.put(
        `xendit_purchase:${external_id}`,
        JSON.stringify(purchaseData)
      );

      // Redirect to frontend
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("purchase_status", "success");
      redirectUrl.searchParams.set("external_id", external_id);
      redirectUrl.searchParams.set("payment_method", "xendit");

      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling Xendit purchase success:", error);
      
      // Redirect to frontend with error
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("purchase_status", "error");
      redirectUrl.searchParams.set("error", error.message);

      return Response.redirect(redirectUrl.toString(), 302);
    }
  }

  async handlePurchaseFailed(request) {
    try {
      const url = new URL(request.url);
      const external_id = url.searchParams.get("external_id");

      if (external_id) {
        // Update purchase status to failed
        const purchaseData = await this.env.USERS_KV.get(
          `xendit_purchase:${external_id}`,
          "json"
        );

        if (purchaseData) {
          purchaseData.status = "FAILED";
          purchaseData.failed_at = new Date().toISOString();
          await this.env.USERS_KV.put(
            `xendit_purchase:${external_id}`,
            JSON.stringify(purchaseData)
          );
        }
      }

      // Redirect to frontend
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("purchase_status", "failed");
      redirectUrl.searchParams.set("external_id", external_id || "unknown");

      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling Xendit purchase failure:", error);
      
      // Redirect to frontend with error
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("purchase_status", "error");
      redirectUrl.searchParams.set("error", error.message);

      return Response.redirect(redirectUrl.toString(), 302);
    }
  }

  async getPurchaseStatus(request) {
    try {
      const url = new URL(request.url);
      const external_id = url.searchParams.get("external_id");

      if (!external_id) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "external_id is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get purchase data from KV
      const purchaseData = await this.env.USERS_KV.get(
        `xendit_purchase:${external_id}`,
        "json"
      );

      if (!purchaseData) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Purchase not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: purchaseData,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting purchase status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get purchase status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async generateQRCode(request) {
    try {
      const body = await request.json();
      const { external_id } = body;

      if (!external_id) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "external_id is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get purchase data
      const purchaseData = await this.env.USERS_KV.get(
        `xendit_purchase:${external_id}`,
        "json"
      );

      if (!purchaseData || purchaseData.payment_method !== "QRIS") {
        return new Response(
          JSON.stringify({
            success: false,
            message: "QRIS purchase not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get QR string from Xendit (you might need to store this in purchase data)
      // For now, return the QR code generation endpoint
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            qr_code_url: `${this.env.APP_URL}/api/xendit/qris/qr-code`,
            external_id,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error generating QR code:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to generate QR code",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}