// src/controllers/subscriptionController.js
import { PayPalService } from "../services/paypalService.js"; // Note the .js extension
import { TierService } from "../services/tierService.js";
import { ResponseService } from "../services/responseService.js";
import { XenditService } from "../services/xenditService.js";
import { TokenService } from "../services/tokenService.js";

export class SubscriptionController {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierService(env);
    this.responseService = new ResponseService();
    this.xenditService = new XenditService(env);
  }

  async createSubscription(request) {
    try {
      console.log("Starting subscription creation...");

      // Extract email from JWT token
      const authHeader = request.headers.get("Authorization");
      const token = authHeader.split(" ")[1];
      const tokenService = new TokenService(this.env);
      const decoded = await tokenService.verifyToken(token);
      const userEmail = decoded.email;

      console.log("User email from token:", userEmail);

      // Get request body
      const body = await request.json();
      console.log("Request body:", body);

      const { tier, addons = { addon1: false, addon2: false } } = body;

      if (!tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Tier is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get tier settings first to validate
      const tierSettings = await this.tierService.getTierSettings();
      console.log("Tier settings:", tierSettings);

      // Validate addons
      if (addons.addon1 !== undefined && typeof addons.addon1 !== "boolean") {
        throw new Error("addon1 must be a boolean value");
      }
      if (addons.addon2 !== undefined && typeof addons.addon2 !== "boolean") {
        throw new Error("addon2 must be a boolean value");
      }

      if (!tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Tier is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Validate tier exists in settings
      if (!tierSettings?.config?.[tier]) {
        return new Response(
          JSON.stringify({
            success: false,
            message: `Invalid tier: ${tier}. Available tiers: ${Object.keys(
              tierSettings?.config || {}
            ).join(", ")}`,
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Log PayPal environment variables
      console.log("PayPal config:", {
        sandboxMode: this.env.PAYPAL_SANDBOX === "true",
        hasClientId: !!this.env.PAYPAL_CLIENT_ID,
        hasClientSecret: !!this.env.PAYPAL_CLIENT_SECRET,
        appUrl: this.env.APP_URL,
      });

      // Create PayPal subscription using email from token
      console.log(
        "Creating PayPal subscription for tier:",
        tier,
        "for user:",
        userEmail
      );
      const subscription = await this.paypalService.createSubscription(
        userEmail,
        tier,
        addons
      );

      console.log("Subscription created:", subscription);

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            subscriptionId: subscription.subscriptionId,
            approvalUrl: subscription.approvalUrl,
            tier: subscription.tier,
            basePrice: subscription.basePrice,
            addons: subscription.addons,
            totalPrice: subscription.totalPrice,
            status: subscription.status,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Subscription creation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to create subscription",
          details: error.stack,
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // Add new endpoint to update addons
  async updateSubscriptionAddons(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const { subscriptionId, addons } = await request.json();

      if (!apiKey) {
        throw new Error("API key is required");
      }

      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      if (!user) {
        throw new Error("Invalid API key");
      }

      const result = await this.paypalService.updateSubscriptionAddons(
        subscriptionId,
        addons
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: result,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      // ... error handling ...
    }
  }

  async getTestEndpoint() {
    try {
      return new Response(
        JSON.stringify({
          success: true,
          message: "Subscription router is working",
          env: {
            hasPayPalConfig: !!(
              this.env.PAYPAL_CLIENT_ID && this.env.PAYPAL_CLIENT_SECRET
            ),
            sandbox: this.env.PAYPAL_SANDBOX === "true",
            appUrl: this.env.APP_URL,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async handleSubscriptionSuccess(subscriptionId) {
    try {
      console.log(
        "Controller handling subscription success for ID:",
        subscriptionId
      );

      if (!subscriptionId) {
        throw new Error("Subscription ID is required");
      }

      // Activate the subscription in PayPal and update local records
      console.log("Calling PayPal service to activate subscription");
      const result = await this.paypalService.activateSubscription(
        subscriptionId
      );
      console.log("PayPal service activation result:", result);

      return result;
    } catch (error) {
      console.error(
        "Error handling subscription success in controller:",
        error
      );
      throw error;
    }
  }

  async createXenditSubscription(request) {
    try {
      const body = await request.json();
      const {
        tier,
        addons = { addon1: false, addon2: false },
        payment_method = null,
        payment_methods = null,
        available_ewallets = null,
        available_retail_outlets = null,
        available_banks = null,
      } = body;

      let finalUserEmail = null;
      let decodedPayload = null;

      const authHeader = request.headers.get("Authorization");
      if (authHeader && authHeader.startsWith("Bearer ")) {
        const token = authHeader.split(" ")[1];
        const tokenService = new TokenService(this.env);
        try {
          // This logic mimics the validation middleware in xenditRoutes.js
          try {
            decodedPayload = await tokenService.verifyToken(token);
            console.log("Standard token verified.");
          } catch (e) {
            console.log("Standard token failed, trying portal token.");
            const portalResult = await tokenService.verifyPortalToken(token);
            if (portalResult && portalResult.success && portalResult.data) {
              decodedPayload = portalResult.data;
              console.log("Portal token verified.");
            } else {
              throw e; // Re-throw original error if portal token also fails
            }
          }

          if (decodedPayload && decodedPayload.email) {
            // Standard user token with email directly
            finalUserEmail = decodedPayload.email;
            console.log(`Extracted email '${finalUserEmail}' directly from standard token`);
          } else if (decodedPayload && decodedPayload.id) {
            // Standard user token with id, get email from user data
            const emailFromId = await this.tierService.getEmailFromUserId(decodedPayload.id);
            if (emailFromId) {
              finalUserEmail = emailFromId;
              console.log(`Resolved email '${finalUserEmail}' from user id '${decodedPayload.id}'`);
            } else {
              console.error(`Could not resolve email for user id '${decodedPayload.id}'`);
            }
          } else if (decodedPayload && decodedPayload.userId) {
            // Portal token with userId, get email from user data
            const emailFromId = await this.tierService.getEmailFromUserId(decodedPayload.userId);
            if (emailFromId) {
              finalUserEmail = emailFromId;
              console.log(`Resolved email '${finalUserEmail}' from portal userId '${decodedPayload.userId}'`);
            } else {
              console.error(`Could not resolve email for portal userId '${decodedPayload.userId}'`);
            }
          }
        } catch (tokenError) {
          console.error("Failed to verify token:", tokenError.message);
        }
      }

      // Fallback to request body if email not found from a valid token
      if (!finalUserEmail) {
        console.log("Could not determine user from token, falling back to request body.");
        finalUserEmail = body.email || body.userEmail;
      }

      
      if (!finalUserEmail) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "User email is required. Please provide userId, email, or valid Authorization token.",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      console.log("Final user email for subscription:", finalUserEmail);

      // Get tier settings
      const tierSettings = await this.tierService.getTierSettings();

      // Validate tier
      if (!tierSettings?.config?.[tier]) {
        return new Response(
          JSON.stringify({
            success: false,
            message: `Invalid tier: ${tier}. Available tiers: ${Object.keys(
              tierSettings?.config || {}
            ).join(", ")}`,
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Calculate price in USD first
      const tierConfig = tierSettings.config[tier];
      let totalAmountUSD = tierConfig.price;
      if (addons.addon1) totalAmountUSD += tierConfig.addon1_price;
      if (addons.addon2) totalAmountUSD += tierConfig.addon2_price;

      // Convert to IDR using RATE_IDR from environment
      const rateIDR = parseFloat(this.env.RATE_IDR) || 15000; // Default to 15000 if not set
      const totalAmount = Math.round(totalAmountUSD * rateIDR); // Round to avoid decimal issues

      console.log(
        `Price calculation: ${totalAmountUSD} USD × ${rateIDR} = ${totalAmount} IDR`
      );

      // Create reference ID
      const referenceId = `sub_${Date.now()}_${Math.random()
        .toString(36)
        .slice(2, 11)}`;

      // Store subscription data
      await this.env.USERS_KV.put(
        `xendit_ref:${referenceId}`,
        JSON.stringify({
          userId: finalUserEmail, // Use finalUserEmail as userId for consistency
          email: finalUserEmail, // Also store email separately
          tier,
          addons,
          amount: totalAmount,
          status: "pending",
          created_at: new Date().toISOString(),
        })
      );

      // Create Xendit invoice with optional payment method pre-selection
      const invoiceData = {
        external_id: referenceId,
        amount: totalAmount,
        payer_email: finalUserEmail,
        description: `Subscription for ${tier} tier`,
        success_redirect_url: `${
          this.env.FRONTEND_URL || "http://localhost:3001"
        }/api/subscriptions/xendit/success`,
        failure_redirect_url: `${
          this.env.FRONTEND_URL || "http://localhost:3001"
        }/api/xendit/callback?status=cancel`,
      };

      // Handle payment method filtering with priority order
      if (
        available_banks &&
        Array.isArray(available_banks) &&
        available_banks.length > 0
      ) {
        // Bank Transfer with specific banks - use bank codes directly in payment_methods
        console.log(
          `Setting up specific bank filter: ${available_banks.join(", ")}`
        );
        invoiceData.payment_methods = available_banks; // Use bank codes directly
      } else if (available_ewallets && Array.isArray(available_ewallets)) {
        // E-wallet with specific providers
        console.log(
          `Setting available e-wallets: ${JSON.stringify(available_ewallets)}`
        );
        invoiceData.payment_methods = ["EWALLET"];
        invoiceData.available_ewallets = available_ewallets;
      } else if (
        available_retail_outlets &&
        Array.isArray(available_retail_outlets)
      ) {
        // Retail outlets
        console.log(
          `Setting available retail outlets: ${JSON.stringify(
            available_retail_outlets
          )}`
        );
        invoiceData.payment_methods = ["RETAIL"];
        invoiceData.available_retail_outlets = available_retail_outlets;
      } else if (
        payment_methods &&
        Array.isArray(payment_methods) &&
        payment_methods.includes("BANK_TRANSFER")
      ) {
        // General bank transfer without specific bank filtering
        console.log(`Setting general bank transfer`);
        invoiceData.payment_methods = ["BANK_TRANSFER"];
      } else if (payment_methods && Array.isArray(payment_methods)) {
        // Direct payment methods array
        console.log(
          `Setting direct payment methods array: ${JSON.stringify(
            payment_methods
          )}`
        );
        invoiceData.payment_methods = payment_methods;
      } else if (payment_method) {
        console.log(`Setting payment method preference: ${payment_method}`);

        // Map single payment method to appropriate format
        const paymentMethodMap = {
          gopay: ["EWALLET"],
          dana: ["EWALLET"],
          ovo: ["EWALLET"],
          qris: ["QRIS"],
          bca: ["BANK_TRANSFER"],
          bni: ["BANK_TRANSFER"],
          bri: ["BANK_TRANSFER"],
          mandiri: ["BANK_TRANSFER"],
        };

        const selectedMethod = payment_method.toLowerCase();

        if (paymentMethodMap[selectedMethod]) {
          // Set the general payment method category
          invoiceData.payment_methods = paymentMethodMap[selectedMethod];

          // For e-wallets, try to set specific channels but fallback gracefully
          if (
            selectedMethod === "gopay" ||
            selectedMethod === "dana" ||
            selectedMethod === "ovo"
          ) {
            try {
              // Only set specific e-wallet if it's supported
              const channelMap = {
                gopay: "GOPAY",
                dana: "DANA",
                ovo: "OVO",
              };

              invoiceData.payment_method_types = {
                EWALLET: [channelMap[selectedMethod]],
              };
            } catch (error) {
              console.warn(
                `Specific e-wallet ${selectedMethod} not supported, using general EWALLET`
              );
              // Fallback to general EWALLET without specific channel
            }
          }
        } else {
          console.warn(
            `Payment method ${payment_method} not supported, using default payment methods`
          );
        }
      }

      console.log(
        "Final invoice data being sent to Xendit:",
        JSON.stringify(invoiceData, null, 2)
      );

      let invoice;
      try {
        invoice = await this.xenditService.createInvoice(invoiceData);
      } catch (error) {
        console.error(
          "Error creating invoice with payment method restriction:",
          error.message
        );

        // If payment method restriction fails, retry without restrictions
        if (
          (payment_method ||
            payment_methods ||
            available_ewallets ||
            available_retail_outlets ||
            available_banks) &&
          error.message.includes("payment method choices did not match")
        ) {
          console.log(
            "Retrying invoice creation without payment method restrictions..."
          );

          const fallbackInvoiceData = {
            external_id: referenceId,
            amount: totalAmount,
            payer_email: finalUserEmail,
            description: `Subscription for ${tier} tier`,
            success_redirect_url: `${this.env.APP_URL}/api/subscriptions/xendit/success?ref=${referenceId}`,
            failure_redirect_url: `${this.env.APP_URL}/dashboard?subscription_status=failed`,
          };

          console.log(
            "Fallback invoice data:",
            JSON.stringify(fallbackInvoiceData, null, 2)
          );
          invoice = await this.xenditService.createInvoice(fallbackInvoiceData);
          console.log("Fallback invoice creation successful");
        } else {
          throw error;
        }
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            invoiceUrl: invoice.invoice_url,
            referenceId: referenceId,
            amount: totalAmount,
            status: "pending",
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error creating Xendit subscription:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to create subscription",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async handleXenditSuccess(request) {
    try {
      const url = new URL(request.url);
      const referenceId = url.searchParams.get("ref");

      if (!referenceId) {
        throw new Error("Missing reference ID");
      }

      // Try to get subscription data from multiple sources
      let subscriptionData = await this.env.USERS_KV.get(
        `xendit_ref:${referenceId}`,
        "json"
      );

      // If not found in xendit_ref, check if it was already processed and stored elsewhere
      if (!subscriptionData) {
        console.log(
          `No subscription data found in xendit_ref:${referenceId}, checking other sources...`
        );

        // Check if there's payment data that might contain subscription info
        const paymentData = await this.env.USERS_KV.get(
          `payment:${referenceId}`
        );
        if (paymentData) {
          const parsedPaymentData = JSON.parse(paymentData);
          console.log("Found payment data:", parsedPaymentData);

          // If payment data contains subscription info, use it
          if (parsedPaymentData.subscription_data) {
            subscriptionData = parsedPaymentData.subscription_data;
            console.log("Using subscription data from payment record");
          }
        }

        // Check purchase data as fallback
        if (!subscriptionData) {
          const purchaseData = await this.env.USERS_KV.get(
            `xendit_purchase:${referenceId}`,
            "json"
          );
          if (purchaseData) {
            console.log(
              "Found purchase data, treating as subscription:",
              purchaseData
            );
            subscriptionData = {
              userId: purchaseData.email,
              tier: purchaseData.tier,
              addons: purchaseData.addons || { addon1: false, addon2: false },
            };
          }
        }

        // If still no data, check if webhook already processed this successfully
        if (!subscriptionData) {
          // This fallback logic was removed because it relied on a hardcoded user email,
          // which is incorrect and a security risk. The system will now rely on
          // other fallback mechanisms like payment and webhook logs.
        }
      }

      if (!subscriptionData) {
        console.log(`No subscription data found for reference: ${referenceId}`);
        console.log(
          "This likely means the webhook already processed and cleaned up the data successfully"
        );

        // Check if there's a payment record that confirms successful processing
        const paymentRecord = await this.env.USERS_KV.get(
          `payment:${referenceId}`,
          "json"
        );
        if (paymentRecord && paymentRecord.status === "PAID") {
          console.log(
            "Found payment record confirming successful payment:",
            paymentRecord
          );

          // Payment was successful, redirect to success page
          const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
          const redirectUrl = new URL("/dashboard", frontendUrl);
          redirectUrl.searchParams.set("subscription_status", "success");
          redirectUrl.searchParams.set("subscription_id", referenceId);
          redirectUrl.searchParams.set(
            "message",
            "Payment processed successfully"
          );

          return Response.redirect(redirectUrl.toString(), 302);
        }

        // If no payment record found either, it might be an older transaction
        // Check recent webhook logs to see if this was processed
        const recentWebhookLogs = await this.env.USERS_KV.list({
          prefix: "xendit_webhook_log:",
          limit: 50,
        });

        for (const logKey of recentWebhookLogs.keys) {
          const logData = await this.env.USERS_KV.get(logKey.name);
          if (
            logData &&
            logData.includes(referenceId) &&
            logData.includes("PAID")
          ) {
            console.log(
              "Found webhook log confirming successful processing:",
              logKey.name
            );

            const frontendUrl =
              this.env.FRONTEND_URL || "http://localhost:3000";
            const redirectUrl = new URL("/dashboard", frontendUrl);
            redirectUrl.searchParams.set("subscription_status", "success");
            redirectUrl.searchParams.set("subscription_id", referenceId);
            redirectUrl.searchParams.set(
              "message",
              "Payment already processed successfully"
            );

            return Response.redirect(redirectUrl.toString(), 302);
          }
        }

        // If we still can't find any record, redirect with unknown status
        console.error(
          `No subscription, payment, or webhook data found for reference: ${referenceId}`
        );
        const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
        const redirectUrl = new URL("/dashboard", frontendUrl);
        redirectUrl.searchParams.set("subscription_status", "unknown");
        redirectUrl.searchParams.set("subscription_id", referenceId);
        redirectUrl.searchParams.set(
          "message",
          "Payment status unclear - please check your subscription status"
        );

        return Response.redirect(redirectUrl.toString(), 302);
      }

      // Update user's tier using upgradeEmailTier instead of updateUserTier
      const userEmail = subscriptionData.userId || subscriptionData.email;

      if (subscriptionData.status === 'processed') {
        console.log(`Subscription ${referenceId} already processed. Proceeding with redirect.`);
      } else if (userEmail) { // Ensure we have a user email before proceeding
        console.log(`Upgrading tier for user: ${userEmail} to tier: ${subscriptionData.tier}`);
        
        await this.tierService.upgradeEmailTier(
            userEmail,
            subscriptionData.tier,
            {
                subscriptionId: referenceId,
                addon1: subscriptionData.addons?.addon1 || false,
                addon2: subscriptionData.addons?.addon2 || false,
            }
        );

        // Mark as processed instead of deleting
        console.log("Marking subscription as processed.");
        subscriptionData.status = 'processed';
        subscriptionData.processedAt = new Date().toISOString();
        await this.env.USERS_KV.put(
            `xendit_ref:${referenceId}`,
            JSON.stringify(subscriptionData),
            { expirationTtl: 86400 } // Keep record for 24 hours
        );
      } else {
        console.error(`No user email found in subscription data for reference: ${referenceId}`);
        // Let it fall through to the redirect. The webhook should handle it if data is correct.
      }

      // Redirect to frontend
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "success");
      redirectUrl.searchParams.set("subscription_id", referenceId);

      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling Xendit success:", error);
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "error");
      redirectUrl.searchParams.set("error", error.message);
      return Response.redirect(redirectUrl.toString(), 302);
    }
  }
}
