import { TierService } from "../services/tierServiceD1.js";
import { UsageHistoryService } from "../services/usageHistoryService.js";
import { ResponseService } from "../services/responseService.js";
import { UserService } from "../services/userServiceD1.js";
import { TokenService } from "../services/tokenService.js";

export class ActivityController {
  constructor(env) {
    this.env = env;
    this.tierService = new TierServiceD1(env);
    this.historyService = new UsageHistoryService(env);
    this.responseService = new ResponseService();
    this.userService = new UserServiceD1(env);
    this.tokenService = new TokenService(env);
  }

  async getActivityDataByEmail(request) {
    try {
      // Token validation
      const authHeader = request.headers.get("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Authorization header with Bearer token is required",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const token = authHeader.split(" ")[1];
      try {
        await this.tokenService.verifyToken(token);
      } catch (tokenError) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid token or token has expired",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const data = await request.json();
      const { email } = data;
      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            data: null,
            error: "Email is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const user = await this.env.USERS_KV.get(
        `email:${email.toLowerCase().trim()}`,
        "json"
      );
      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            data: null,
            error: "User not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get domains using UserService
      const domains = await this.userService.getDomains(email);
      const emailTier = await this.tierService.getEmailTier(email);

      // Get history for each domain using UsageHistoryService
      const allHistory = [];
      let totalImagesUsage = 0;
      let totalContentUsage = 0;
      let totalTitleUsage = 0;
      let imagesQuota = 0;
      let contentQuota = 0;
      let titleQuota = 0;

      for (const domain of domains) {
        const domainHistory = await this.historyService.getUserHistory(
          domain.userId,
          {
            limit: 100,
            page: 1,
          }
        );

        // Get tier status for this domain
        const tierStatus = await this.tierService.getUserTierStatus(
          domain.userId,
          domain.api_key
        );

        // Accumulate usage and quota
        totalImagesUsage += tierStatus.imagesUsage || 0;
        totalContentUsage += tierStatus.contentUsage || 0;
        totalTitleUsage += tierStatus.titleUsage || 0;
        imagesQuota = Math.max(imagesQuota, tierStatus.imagesQuota || 0);
        contentQuota = Math.max(contentQuota, tierStatus.contentQuota || 0);
        titleQuota = Math.max(titleQuota, tierStatus.titleQuota || 0);

        if (domainHistory && domainHistory.entries) {
          const historyWithDomain = domainHistory.entries.map((entry) => ({
            ...entry,
            domain: domain.domain,
          }));
          allHistory.push(...historyWithDomain);
        }
      }

      // Sort by timestamp descending (newest first)
      allHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Calculate metrics
      const totalScans = allHistory.length;
      const lastScanTime =
        allHistory.length > 0 ? allHistory[0].timestamp : null;

      const response = {
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            websites: allHistory.map((entry) => ({
              id: entry.id,
              userId: entry.userId,
              type: entry.type,
              source: entry.source,
              timestamp: entry.timestamp,
              metadata: entry.metadata || {},
              domain: entry.domain,
            })),
            metrics: {
              totalScans,
              lastScanTime,
            },
            quotaUsage: {
              images: {
                used: totalImagesUsage,
                remaining: Math.max(0, imagesQuota - totalImagesUsage),
                total: imagesQuota,
                percentageUsed: (
                  (totalImagesUsage / imagesQuota) *
                  100
                ).toFixed(2),
              },
              content: {
                used: totalContentUsage,
                remaining: Math.max(0, contentQuota - totalContentUsage),
                total: contentQuota,
                percentageUsed: (
                  (totalContentUsage / contentQuota) *
                  100
                ).toFixed(2),
              },
              title: {
                used: totalTitleUsage,
                remaining: Math.max(0, titleQuota - totalTitleUsage),
                total: titleQuota,
                percentageUsed: ((totalTitleUsage / titleQuota) * 100).toFixed(
                  2
                ),
              },
            },
            accountDetails: {
              limits: {
                currentUsage: {
                  websites: domains.length,
                },
                restrictions: {
                  websiteLimit: 10, // Can be adjusted based on tier
                },
              },
              subscription: {
                currentPlan: emailTier.tier || "BASIC",
                startDate: emailTier.startDate || "2024-02-21T00:00:00Z",
                endDate: emailTier.expirationDate || "2025-02-21T00:00:00Z",
                status: emailTier.status || "active",
              },
            },
          },
        },
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Activity error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          data: null,
          error: error.message || "Failed to fetch activity data",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
