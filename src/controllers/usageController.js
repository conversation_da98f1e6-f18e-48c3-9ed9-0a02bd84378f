import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import { ResponseService } from "../services/responseService";
import { ApiKeyService } from "../services/apiKeyService";
import { UsageHistoryService } from "../services/usageHistoryService";
import { TokenService } from "../services/tokenService";
import { UserService } from "../services/userService";

export class UsageController {
  constructor(env) {
    this.env = env;
    this.tierService = new TierServiceD1(env);
    this.historyService = new UsageHistoryService(env);
    this.responseService = new ResponseService();
    this.tokenService = new TokenService(env);
    this.userService = new UserServiceD1(env);
    this.apiKeyService = new ApiKeyServiceD1(env);
  }

  async validateToken(token) {
    try {
      return await this.jwtService.verifyToken(token);
    } catch (error) {
      console.error("Error validating token:", error);
      return null;
    }
  }

  async _validateApiKey(request, requireDomain = true) {
    const apiKey = request.headers.get("x-sps-key");
    const requestDomain = request.headers.get("x-sps-domain");

    if (!apiKey) {
      throw new Error("API Key is required");
    }

    if (requireDomain && !requestDomain) {
      throw new Error("Domain is required in x-sps-domain header");
    }

    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("Invalid API Key");
    }

    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }

    const domain = user.domains?.find((d) => d.api_key === apiKey);
    if (!domain) {
      throw new Error("Invalid API Key");
    }

    if (requireDomain && requestDomain && domain.domain !== requestDomain) {
      throw new Error(
        "Failed activate api key, use the correct registered domain !"
      );
    }

    return {
      userId: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: apiKey,
    };
  }

  async trackImageUsage(request) {
    try {
      const user = await this._validateApiKey(request, true);
      const data = await request.json();

      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }

      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.userId,
        "images",
        user.api_key
      );
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );

      await this.historyService.trackUsage(
        user.userId,
        {
          type: "images",
          source: data.source,
          timestamp: data.timestamp,
          metadata: data.metadata,
        },
        user.domain
      );

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            type: "images",
            usage: updatedUsage,
            source: data.source,
            timestamp: data.timestamp,
            domain: user.domain,
            tierStatus: {
              current: updatedUsage,
              remaining: tierStatus.remainingImagesQuota,
              limit: tierStatus.imagesQuota,
            },
          })
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async trackContentUsage(request) {
    try {
      const user = await this._validateApiKey(request, true);
      const data = await request.json();

      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }

      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.userId,
        "content",
        user.api_key
      );
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );

      await this.historyService.trackUsage(
        user.userId,
        {
          type: "content",
          source: data.source,
          timestamp: data.timestamp,
          metadata: data.metadata,
        },
        user.domain
      );

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            {
              type: "content",
              usage: updatedUsage,
              source: data.source,
              timestamp: data.timestamp,
              domain: user.domain,
              tierStatus: {
                current: updatedUsage,
                remaining: tierStatus.remainingContentQuota,
                limit: tierStatus.contentQuota,
              },
            },
            "Content usage tracked successfully"
          )
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getUsageStats(request) {
    try {
      const user = await this._validateApiKey(request, false);
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );
      const emailTier = await this.tierService.getEmailTier(user.email);

      let usageStats;

      if (emailTier.tier === TierService.TIERS.STARTER) {
        const domainImagesUsage = await this.tierService.getDomainQuotaUsage(
          user.domain,
          "images"
        );
        const domainContentUsage = await this.tierService.getDomainQuotaUsage(
          user.domain,
          "content"
        );
        const domainTitleUsage = await this.tierService.getDomainQuotaUsage(
          user.domain,
          "title"
        );

        const tierConfig =
          TierService.DEFAULT_TIER_CONFIG[TierService.TIERS.STARTER];

        usageStats = {
          images: {
            used: domainImagesUsage || 0,
            remaining: Math.max(0, tierConfig.imagesQuota - domainImagesUsage),
            total: tierConfig.imagesQuota,
            percentageUsed: (
              ((domainImagesUsage || 0) / tierConfig.imagesQuota) *
              100
            ).toFixed(2),
          },
          content: {
            used: domainContentUsage || 0,
            remaining: Math.max(
              0,
              tierConfig.contentQuota - domainContentUsage
            ),
            total: tierConfig.contentQuota,
            percentageUsed: (
              ((domainContentUsage || 0) / tierConfig.contentQuota) *
              100
            ).toFixed(2),
          },
          title: {
            used: domainTitleUsage || 0,
            remaining: Math.max(0, tierConfig.titleQuota - domainTitleUsage),
            total: tierConfig.titleQuota,
            percentageUsed: (
              ((domainTitleUsage || 0) / tierConfig.titleQuota) *
              100
            ).toFixed(2),
          },
        };
      } else {
        usageStats = {
          images: {
            used: tierStatus.imagesUsage || 0,
            remaining: tierStatus.remainingImagesQuota,
            total: tierStatus.imagesQuota,
            percentageUsed: (
              ((tierStatus.imagesUsage || 0) / tierStatus.imagesQuota) *
              100
            ).toFixed(2),
          },
          content: {
            used: tierStatus.contentUsage || 0,
            remaining: tierStatus.remainingContentQuota,
            total: tierStatus.contentQuota,
            percentageUsed: (
              ((tierStatus.contentUsage || 0) / tierStatus.contentQuota) *
              100
            ).toFixed(2),
          },
          title: {
            used: tierStatus.titleUsage || 0,
            remaining: tierStatus.remainingTitleQuota,
            total: tierStatus.titleQuota,
            percentageUsed: (
              ((tierStatus.titleUsage || 0) / tierStatus.titleQuota) *
              100
            ).toFixed(2),
          },
        };
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            ...usageStats,
            tier: {
              name: tierStatus.tierName,
              current: tierStatus.currentTier,
              expirationDate: tierStatus.expirationDate,
              isExpired: tierStatus.isExpired,
              price: tierStatus.price,
            },
            addons: {
              addon1: {
                enabled: tierStatus.addon1,
                price: tierStatus.addon1_price,
                features: tierStatus.addon1_detail,
              },
              addon2: {
                enabled: tierStatus.addon2,
                price: tierStatus.addon2_price,
                features: tierStatus.addon2_detail,
              },
            },
            features: tierStatus.features,
          })
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getUsageHistory(request) {
    try {
      const user = await this._validateApiKey(request, false);
      const url = new URL(request.url);

      const options = {
        startDate: url.searchParams.get("startDate"),
        endDate: url.searchParams.get("endDate"),
        type: url.searchParams.get("type"),
        limit: parseInt(url.searchParams.get("limit") || "100"),
        page: parseInt(url.searchParams.get("page") || "1"),
      };

      const history = await this.historyService.getUserHistory(
        user.userId,
        options
      );

      return new Response(
        JSON.stringify(this.responseService.formatSuccess(history)),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getDailyUsage(request) {
    try {
      const user = await this._validateApiKey(request, false);
      const url = new URL(request.url);

      const startDate = url.searchParams.get("startDate");
      const endDate = url.searchParams.get("endDate");

      const dailyUsage = await this.historyService.getDailyUsage(
        user.userId,
        startDate,
        endDate
      );

      return new Response(
        JSON.stringify(this.responseService.formatSuccess(dailyUsage)),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async trackTitleUsage(request) {
    try {
      const user = await this._validateApiKey(request, true);
      const data = await request.json();

      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }

      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.userId,
        "title",
        user.api_key
      );
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );

      await this.historyService.trackUsage(
        user.userId,
        {
          type: "title",
          source: data.source,
          timestamp: data.timestamp,
          metadata: data.metadata,
        },
        user.domain
      );

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            tracked: true,
            currentUsage: updatedUsage,
            remainingQuota: tierStatus.remainingTitleQuota,
            timestamp: data.timestamp,
            domain: user.domain,
            tierStatus: {
              current: updatedUsage,
              remaining: tierStatus.remainingTitleQuota,
              limit: tierStatus.titleQuota,
            },
          })
        ),
        {
          status: 201,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getEmailUsageHistory(request) {
    try {
      // Validate JWT token
      const authHeader = request.headers.get("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Authorization header with Bearer token is required",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const token = authHeader.split(" ")[1];
      try {
        await this.tokenService.verifyToken(token);
      } catch (tokenError) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Invalid token or token has expired",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get email from request body
      const data = await request.json();
      const { email } = data;

      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Email is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get query parameters
      const url = new URL(request.url);
      const options = {
        type: url.searchParams.get("type"),
        limit: parseInt(url.searchParams.get("limit") || "100"),
        page: parseInt(url.searchParams.get("page") || "1"),
      };

      // Get domains using UserService
      const domains = await this.userService.getDomains(email);
      if (!domains || domains.length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "No domains found for this email",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get history for each domain using UsageHistoryService
      const allHistory = [];
      for (const domain of domains) {
        const domainHistory = await this.historyService.getUserHistory(
          domain.userId,
          options
        );
        if (domainHistory && domainHistory.entries) {
          // Add domain information to each history entry
          const historyWithDomain = domainHistory.entries.map((entry) => ({
            ...entry,
            domain: domain.domain,
          }));
          allHistory.push(...historyWithDomain);
        }
      }

      // Sort by timestamp descending (newest first)
      allHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Calculate pagination
      const startIndex = (options.page - 1) * options.limit;
      const endIndex = startIndex + options.limit;
      const paginatedHistory = allHistory.slice(startIndex, endIndex);

      // Calculate total pages
      const totalPages = Math.ceil(allHistory.length / options.limit);

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            history: paginatedHistory,
            pagination: {
              currentPage: options.page,
              totalPages,
              totalItems: allHistory.length,
              itemsPerPage: options.limit,
            },
          })
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error in getEmailUsageHistory:", error);
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
