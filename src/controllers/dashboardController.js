import { TierService } from "../services/tierServiceD1.js";
import { UsageHistoryService } from "../services/usageHistoryService.js";
import { ResponseService } from "../services/responseService.js";
import { UserService } from "../services/userServiceD1.js";
import { TokenService } from "../services/tokenService.js";

export class DashboardController {
  constructor(env) {
    this.env = env;
    this.tierService = new TierServiceD1(env);
    this.historyService = new UsageHistoryService(env);
    this.responseService = new ResponseService();
    this.userService = new UserServiceD1(env);
    this.tokenService = new TokenService(env);
  }

  async getDashboardDataByEmail(request) {
    try {
      // pengecekan token
      const authHeader = request.headers.get("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Authorization header with Bearer token is required",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const token = authHeader.split(" ")[1];
      try {
        await this.tokenService.verifyToken(token);
      } catch (tokenError) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid token or token has expired",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const data = await request.json();
      const { email } = data;

      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            data: null,
            error: "Email is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get user domains using UserService
      const domains = await this.userService.getDomains(email);

      // Get user's tier status
      const emailTier = await this.tierService.getEmailTier(email);

      // Get usage statistics for all domains
      const websites = [];
      for (const domain of domains) {
        // Get daily usage for the domain
        const dailyUsage = await this.historyService.getDailyUsage(
          domain.api_key
        );

        // Calculate total usage from daily entries
        const totalCreditsUsed = dailyUsage.reduce((total, day) => {
          return (
            total + (day.images || 0) + (day.content || 0) + (day.title || 0)
          );
        }, 0);

        const totalScans = dailyUsage.reduce((total, day) => {
          return total + day.details.length;
        }, 0);

        websites.push({
          domain: domain.domain,
          status: domain.status || "pending",
          credits: domain.credits || 0,
          lastScan: domain.lastScan || domain.activatedAt || domain.createdAt,
          key: domain.api_key,
          totalCreditsUsed,
          totalScans,
        });
      }

      // Calculate total metrics across all domains
      const totalMetrics = websites.reduce(
        (acc, site) => {
          acc.totalCreditsUsed += site.totalCreditsUsed;
          acc.totalScans += site.totalScans;
          return acc;
        },
        { totalCreditsUsed: 0, totalScans: 0 }
      );

      const response = {
        success: true,
        data: {
          user: {
            accountDetails: {
              subscription: {
                currentPlan: emailTier.tier || "STARTER",
              },
              limits: {
                currentUsage: {
                  websites: websites.length,
                },
                restrictions: {
                  websiteLimit: emailTier.websiteLimit || 5,
                  creditLimit: emailTier.creditLimit || 100,
                },
              },
            },
            metrics: {
              totalCreditsUsed: totalMetrics.totalCreditsUsed,
              totalScans: totalMetrics.totalScans,
            },
            websites,
          },
        },
        error: null,
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Dashboard error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          data: null,
          error: error.message || "Failed to fetch dashboard data",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getActivityDataByEmail(request) {
    try {
      // Pengecekan token (sama seperti endpoint dashboard)
      const authHeader = request.headers.get("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Authorization header with Bearer token is required",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const token = authHeader.split(" ")[1];
      try {
        await this.tokenService.verifyToken(token);
      } catch (tokenError) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid token or token has expired",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const data = await request.json();
      const { email } = data;

      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            data: null,
            error: "Email is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Dapatkan data pengguna berdasarkan email
      const user = await this.env.USERS_KV.get(
        `email:${email.toLowerCase().trim()}`,
        "json"
      );
      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            data: null,
            error: "User not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Dapatkan domain pengguna
      const domains = await this.userService.getDomains(email);

      // Dapatkan riwayat scan untuk setiap domain dari UsageHistoryService
      const websites = [];
      let totalScans = 0;
      let lastScanTime = null;

      for (const domain of domains) {
        // Dapatkan riwayat penggunaan harian dari UsageHistoryService
        const dailyUsage = await this.historyService.getDailyUsage(
          domain.api_key,
          "2024-01-01", // Bisa disesuaikan dengan parameter atau default
          new Date().toISOString().split("T")[0]
        );

        // Simulasikan riwayat scan berdasarkan penggunaan (misalnya, setiap entri dalam dailyUsage adalah scan)
        const scanHistory = [];
        dailyUsage.forEach((day) => {
          day.details.forEach((detail) => {
            scanHistory.push({
              id: detail.id,
              date: detail.time,
              status: Math.random() > 0.3 ? "completed" : "failed", // Simulasi status acak
            });
            totalScans++;
            if (
              !lastScanTime ||
              new Date(detail.time) > new Date(lastScanTime)
            ) {
              lastScanTime = detail.time;
            }
          });
        });

        websites.push({
          domain: domain.domain,
          scanHistory: scanHistory
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 10), // Batasi 10 entri terbaru
        });
      }

      // Dapatkan status tier/subscription dari TierService
      const emailTier = await this.tierService.getEmailTier(email);
      const tierSettings = await this.tierService.getTierSettings();
      const tierConfig = tierSettings.config[emailTier.tier || "starter"];

      // Siapkan respons sesuai format yang diminta
      const response = {
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            websites: websites,
            metrics: {
              totalScans: totalScans || 0,
              lastScanTime: lastScanTime || null,
            },
            accountDetails: {
              limits: {
                currentUsage: {
                  websites: domains.length,
                },
                restrictions: {
                  websiteLimit: 10, // Bisa disesuaikan dengan tier
                },
              },
              subscription: {
                currentPlan: emailTier.tier || "BASIC",
                startDate: emailTier.startDate || "2024-02-21T00:00:00Z",
                endDate: emailTier.expirationDate || "2025-02-21T00:00:00Z",
                status: emailTier.status || "active",
              },
            },
          },
        },
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Activity error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          data: null,
          error: error.message || "Failed to fetch activity data",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
