// src/index.js
import { Router } from "itty-router";
import { createUserRouter } from "./routes/userRoutes";
import { createTierRouter } from "./routes/tierRoutes";
import { createDebugRouter } from "./routes/debugRoutes";
import { createSubscriptionRouter } from "./routes/subscriptionRoutes";
import { swaggerDocument, getSwaggerHTML } from "./swagger/swagger";
import { TierService as TierServiceD1 } from "./services/tierServiceD1.js";
import { EmailQueueService as EmailQueueServiceD1 } from "./services/emailQueueServiceD1.js";
import { createUsageRouter } from "./routes/usageRoutes";
import { createWebhookRouter } from "./routes/webhookRoutes";
import { createWebhookTestRouter } from "./routes/webhookTestRoutes";
import emailQueueHandler from "./queues/emailQueueHandler";
import { createXenditRouter } from "./routes/xenditRoutes";
import { createPayPalRouter } from "./routes/paypalRoutes";
import { createDashboardRouter } from "./routes/dashboardRoutes";
import { createActivityRouter } from "./routes/activityRoutes";
import { createPurchaseRouter } from "./routes/purchaseRoutes";
import { createPaymentRouter } from "./routes/paymentRoutes";
import { createPortalRouter } from "./routes/portalRoutes";
import { createMigrationRouter } from "./routes/migrationRoutes";

// CORS headers configuration
function getCorsHeaders(origin) {
  // Check if origin is from supersense-ett.pages.dev or any of its subdomains
  let isAllowedOrigin = false;

  if (origin) {
    // Parse the origin URL
    try {
      const url = new URL(origin);
      const hostname = url.hostname;

      // Check if it's the main domain or any subdomain
      isAllowedOrigin =
        hostname === "supersense-ett.pages.dev" ||
        hostname.endsWith(".supersense-ett.pages.dev");

      // Also ensure it's using HTTPS
      isAllowedOrigin = isAllowedOrigin && url.protocol === "https:";
    } catch (e) {
      // Invalid URL, not allowed
      isAllowedOrigin = false;
    }
  }

  // For development, you might want to be more permissive
  // Uncomment the line below to allow all origins during development
  // isAllowedOrigin = true;

  // Log for debugging (remove in production)
  console.log(`Origin: ${origin}, Allowed: ${isAllowedOrigin}`);

  return {
    "Access-Control-Allow-Origin": isAllowedOrigin ? origin : "null",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, x-sps-key",
    "Access-Control-Max-Age": "86400",
    "Access-Control-Allow-Credentials": "true",
  };
}

// Helper function to handle OPTIONS requests
function handleOptions(request) {
  const origin = request.headers.get("Origin");
  const corsHeaders = getCorsHeaders(origin);

  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  });
}

// Helper function to add CORS headers to any response
function addCorsHeaders(response, request) {
  const origin = request.headers.get("Origin");
  const corsHeaders = getCorsHeaders(origin);

  const newHeaders = new Headers(response.headers);
  Object.entries(corsHeaders).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders,
  });
}

const router = Router();

// Initialize tier settings
async function initializeTierSettings(env) {
  const tierService = new TierServiceD1(env);
  await tierService.initializeTierSettings();
  console.log("Tier settings initialized");
}

// Process email queue function
async function processEmailQueue(env) {
  try {
    console.log("Starting scheduled email queue processing...");
    const emailQueueService = new EmailQueueServiceD1(env);
    const beforeStatus = await emailQueueService.getQueueStatus();
    console.log("Queue status before processing:", beforeStatus);
    const results = await emailQueueService.processQueue(true);
    console.log("Queue processing results:", results);
    const afterStatus = await emailQueueService.getQueueStatus();
    console.log("Queue status after processing:", afterStatus);
    return { beforeStatus, results, afterStatus };
  } catch (error) {
    console.error("Error processing email queue:", error);
    throw error;
  }
}

// Handle OPTIONS requests for all routes
router.options("*", handleOptions);

// Swagger documentation route
router.get("/api/docs", (request) => {
  const origin = request.headers.get("Origin");
  const corsHeaders = getCorsHeaders(origin);

  const headers = new Headers({
    "content-type": "text/html;charset=UTF-8",
  });

  Object.entries(corsHeaders).forEach(([key, value]) => {
    headers.set(key, value);
  });

  return new Response(getSwaggerHTML(swaggerDocument), {
    headers: headers,
  });
});

// Mount subscription routes (deprecated - use /api/paypal/subscriptions or /api/xendit/subscriptions)
router.all("/api/subscriptions/*", async (request, env) => {
  try {
    const subscriptionRouter = createSubscriptionRouter(env);
    const response = await subscriptionRouter.handle(request);
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error("Subscription route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      ),
      request
    );
  }
});

// Mount tier routes
router.all("/api/tiers/*", async (request, env) => {
  const tierRouter = createTierRouter(env);
  const response = await tierRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount usage routes
router.all("/api/usage/*", async (request, env) => {
  const usageRouter = createUsageRouter(env);
  const response = await usageRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount user routes
router.all("/api/users/*", async (request, env) => {
  const userRouter = createUserRouter(env);
  const response = await userRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount portal routes
router.all("/api/portal/*", async (request, env) => {
  try {
    const portalRouter = createPortalRouter(env);
    const response = await portalRouter.handle(request);
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error("Portal route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      ),
      request
    );
  }
});

// Mount xendit routes
router.all("/api/xendit/*", async (request, env) => {
  const xenditRouter = createXenditRouter(env);
  const response = await xenditRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount PayPal routes
router.all("/api/paypal/*", async (request, env) => {
  try {
    const paypalRouter = createPayPalRouter(env);
    const response = await paypalRouter.handle(request);
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error("PayPal route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      ),
      request
    );
  }
});

// Mount dashboard routes
router.all("/api/dashboard/*", async (request, env) => {
  const dashboardRouter = createDashboardRouter(env);
  const response = await dashboardRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount debug routes
router.all("/api/debug/*", async (request, env) => {
  try {
    const debugRouter = createDebugRouter(env);
    const response = await debugRouter.handle(request);
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error("Debug route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      ),
      request
    );
  }
});

// Mount activity routes
router.all("/api/activity/*", async (request, env) => {
  const activityRouter = createActivityRouter(env);
  const response = await activityRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount purchase routes (deprecated - use /api/paypal/purchases)
router.all("/api/purchases/*", async (request, env) => {
  const purchaseRouter = createPurchaseRouter(env);
  const response = await purchaseRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount payment routes
router.all("/api/payment/*", async (request, env) => {
  const paymentRouter = createPaymentRouter(env);
  const response = await paymentRouter.handle(request);
  return addCorsHeaders(response, request);
});

// simulasi test paypal
router.all("/api/test/webhooks/*", async (request, env) => {
  const testRouter = createWebhookTestRouter(env);
  const response = await testRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount webhook routes
router.all("/api/webhooks/*", async (request, env) => {
  const webhookRouter = createWebhookRouter(env);
  const response = await webhookRouter.handle(request);
  return addCorsHeaders(response, request);
});

// Mount migration routes
router.all("/api/migration/*", async (request, env) => {
  try {
    const migrationRouter = createMigrationRouter(env);
    const response = await migrationRouter.handle(request);
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error("Migration route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      ),
      request
    );
  }
});

// 404 handler
router.all("*", (request) =>
  addCorsHeaders(new Response("Not Found", { status: 404 }), request)
);

// Export worker
export default {
  // Regular request handler
  async fetch(request, env, ctx) {
    try {
      await initializeTierSettings(env);

      // Handle CORS preflight requests
      if (request.method === "OPTIONS") {
        return handleOptions(request);
      }

      // For manual queue processing endpoint
      if (
        request.method === "POST" &&
        request.url.endsWith("/api/debug/process-queue")
      ) {
        const results = await processEmailQueue(env);
        return addCorsHeaders(
          new Response(
            JSON.stringify({
              success: true,
              message: "Queue processed manually",
              ...results,
            }),
            {
              headers: { "Content-Type": "application/json" },
            }
          ),
          request
        );
      }

      const response = await router.handle(request, env, ctx);
      return addCorsHeaders(response, request);
    } catch (error) {
      console.error("Worker error:", error);
      return addCorsHeaders(
        new Response(
          JSON.stringify({
            success: false,
            error: error.message,
          }),
          {
            status: 500,
            headers: { "Content-Type": "application/json" },
          }
        ),
        request
      );
    }
  },

  // Scheduled handler for cron
  async scheduled(event, env, ctx) {
    console.log(`Cron triggered: ${event.cron} at ${new Date().toISOString()}`);
    try {
      ctx.waitUntil(processEmailQueue(env));
    } catch (error) {
      console.error("Scheduled job error:", error);
    }
  },

  // Add the queue handler
  queue: emailQueueHandler.queue,
};
